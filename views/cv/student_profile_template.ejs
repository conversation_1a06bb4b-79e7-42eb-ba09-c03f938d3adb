<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= student.name %> - Student Profile</title>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 16px;
            line-height: 1.3;
            color: #000;
            margin: 0;
            padding: 10px;
            background: white;
        }

        .profile-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            padding: 15mm;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #000;
        }

        .header h1 {
            color: #000;
            margin: 0;
            font-size: 22px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .header h2 {
            color: #000;
            margin: 5px 0;
            font-size: 18px;
            font-weight: normal;
        }

        .header .class-info {
            color: #000;
            font-size: 16px;
            margin-top: 3px;
        }
        
        .section {
            margin-bottom: 15px;
            page-break-inside: avoid;
        }

        .section-title {
            background: #000;
            color: white;
            padding: 8px 12px;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 16px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-bottom: 10px;
        }

        .info-item {
            display: flex;
            align-items: center;
            padding: 6px 0;
            border-bottom: 1px solid #ccc;
        }

        .info-label {
            font-weight: bold;
            color: #000;
            min-width: 120px;
            margin-right: 10px;
        }

        .info-value {
            color: #000;
            flex: 1;
        }

        .single-column {
            grid-template-columns: 1fr;
        }

        .address-item {
            background: white;
            padding: 8px;
            margin-bottom: 6px;
            border: 1px solid #000;
            border-left: 3px solid #000;
        }

        .address-label {
            font-weight: bold;
            color: #000;
            margin-bottom: 4px;
            font-size: 16px;
        }

        .address-value {
            color: #000;
            font-size: 16px;
            line-height: 1.4;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #000;
            color: #000;
            font-size: 14px;
        }

        @media print {
            body { margin: 0; padding: 0; }
            .profile-container { padding: 10mm; }
        }
    </style>
</head>
<body>
    <div class="profile-container">
        <!-- Header Section -->
        <div class="header">
            <h1><%= student.name || 'Student Name' %></h1>
            <h2>Student ID: <%= student.student_id || 'N/A' %></h2>
            <div class="class-info"><%= student.class || '' %> <%= student.section || '' %> <%= student.stream || '' %> | Session: <%= student.session || 'N/A' %></div>
        </div>

        <!-- Personal Information -->
        <div class="section">
            <div class="section-title">Personal Information</div>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Full Name:</div>
                    <div class="info-value"><%= student.name || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Student ID:</div>
                    <div class="info-value"><%= student.student_id || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Father's Name:</div>
                    <div class="info-value"><%= student.father_name || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Mother's Name:</div>
                    <div class="info-value"><%= student.mother_name || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Date of Birth:</div>
                    <div class="info-value"><%= student.dob ? new Date(student.dob).toLocaleDateString('en-IN') : 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Gender:</div>
                    <div class="info-value"><%= student.gender || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Religion:</div>
                    <div class="info-value"><%= student.religion_name || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Caste Category:</div>
                    <div class="info-value"><%= student.caste_category_name || 'N/A' %></div>
                </div>
            </div>
        </div>

        <!-- Academic Details -->
        <div class="section">
            <div class="section-title">Academic Details</div>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Class:</div>
                    <div class="info-value"><%= student.class || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Section:</div>
                    <div class="info-value"><%= student.section || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Stream:</div>
                    <div class="info-value"><%= student.stream || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Trade:</div>
                    <div class="info-value"><%= student.trade || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Roll Number:</div>
                    <div class="info-value"><%= student.roll_no || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Session:</div>
                    <div class="info-value"><%= student.session || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Medium:</div>
                    <div class="info-value"><%= student.medium_name || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Room Number:</div>
                    <div class="info-value"><%= student.room_number || 'N/A' %></div>
                </div>
            </div>
        </div>

        <!-- Contact & Address -->
        <div class="section">
            <div class="section-title">Contact & Address</div>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Contact Number:</div>
                    <div class="info-value"><%= student.contact_no || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Village/Ward:</div>
                    <div class="info-value"><%= student.village_ward || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Gram Panchayat:</div>
                    <div class="info-value"><%= student.gram_panchayat || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Pin Code:</div>
                    <div class="info-value"><%= student.pin_code || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">District:</div>
                    <div class="info-value"><%= student.district_name || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">State:</div>
                    <div class="info-value"><%= student.state_name || 'N/A' %></div>
                </div>
            </div>
            <% if (student.cur_address) { %>
            <div class="info-grid single-column">
                <div class="address-item">
                    <div class="address-label">Current Address:</div>
                    <div class="address-value"><%= student.cur_address %></div>
                </div>
            </div>
            <% } %>
        </div>

        <!-- Administrative Details -->
        <div class="section">
            <div class="section-title">Administrative Details</div>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Admission Number:</div>
                    <div class="info-value"><%= student.admission_no || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Admission Date:</div>
                    <div class="info-value"><%= student.admission_date ? new Date(student.admission_date).toLocaleDateString('en-IN') : 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">UDISE Code:</div>
                    <div class="info-value"><%= student.udise_code || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Bank Name:</div>
                    <div class="info-value"><%= student.bank_name || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">IFSC Code:</div>
                    <div class="info-value"><%= student.ifsc_code || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Account Holder:</div>
                    <div class="info-value"><%= student.account_holder_name || 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Account Holder Code:</div>
                    <div class="info-value"><%= student.account_holder_code || 'N/A' %></div>
                </div>
            </div>
        </div>

        <!-- Health & Welfare -->
        <div class="section">
            <div class="section-title">Health & Welfare</div>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Height:</div>
                    <div class="info-value"><%= student.height ? student.height + ' cm' : 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Weight:</div>
                    <div class="info-value"><%= student.weight ? student.weight + ' kg' : 'N/A' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">BPL Status:</div>
                    <div class="info-value"><%= student.bpl || 'No' %></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Disability Status:</div>
                    <div class="info-value"><%= student.disability || 'No' %></div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>Student Management System</strong></p>
            <p>Generated on <%= new Date().toLocaleDateString('en-IN') %> at <%= new Date().toLocaleTimeString('en-IN') %></p>
        </div>
    </div>
</body>
</html>
