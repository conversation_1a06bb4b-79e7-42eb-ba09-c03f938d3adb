/**
 * Teacher Details JavaScript
 * Handles teacher details page functionality including modal, search, and filters
 */

console.log('🚀 Teacher Details JavaScript loaded!');

$(document).ready(function() {
    console.log('✅ Teacher Details System Initialized!');

    // Global variables
    let currentTeacherData = null;
    let currentTeacherId = null;

    // Initialize functionality
    initializeEventHandlers();
    initializeSearch();
    initializeFilters();

    /**
     * Initialize all event handlers
     */
    function initializeEventHandlers() {
        // View teacher button
        $(document).on('click', '.viewTeacherBtn', handleViewTeacher);
        
        // Edit teacher button
        $(document).on('click', '.editTeacherBtn', handleEditTeacher);
        
        // Generate CV button
        $(document).on('click', '.generateCVBtn', handleGenerateCV);
        
        // Modal close buttons
        $(document).on('click', '#closeModalBtn', closeModal);
        
        // Download CV from modal
        $(document).on('click', '#downloadCVBtn', handleDownloadCV);
        
        // Tab navigation
        $(document).on('click', '.tab-btn', handleTabSwitch);
        
        // Refresh data button
        $(document).on('click', '#refreshData', handleRefreshData);
        
        // Export data button
        $(document).on('click', '#exportData', handleExportData);
        
        // Modal background click to close
        $(document).on('click', '#teacherModal', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Escape key to close modal
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && !$('#teacherModal').hasClass('hidden')) {
                closeModal();
            }
        });
    }

    /**
     * Initialize search functionality
     */
    function initializeSearch() {
        $('#searchInput').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            filterTeachers();
        });
    }

    /**
     * Initialize filter functionality
     */
    function initializeFilters() {
        $('#departmentFilter, #statusFilter').on('change', function() {
            filterTeachers();
        });
    }

    /**
     * Filter teachers based on search and filters
     */
    function filterTeachers() {
        const searchTerm = $('#searchInput').val().toLowerCase();
        const departmentFilter = $('#departmentFilter').val();
        const statusFilter = $('#statusFilter').val();

        $('.teacher-row').each(function() {
            const $row = $(this);
            const name = $row.data('name') || '';
            const email = $row.data('email') || '';
            const department = $row.data('department') || '';
            const status = $row.data('status') || '';
            
            let showRow = true;
            
            // Search filter
            if (searchTerm && !name.includes(searchTerm) && !email.includes(searchTerm)) {
                showRow = false;
            }
            
            // Department filter
            if (departmentFilter && department !== departmentFilter) {
                showRow = false;
            }
            
            // Status filter
            if (statusFilter && status !== statusFilter) {
                showRow = false;
            }
            
            $row.toggle(showRow);
        });
    }

    /**
     * Handle view teacher button click
     */
    function handleViewTeacher(e) {
        e.preventDefault();
        const teacherId = $(this).data('teacher-id');
        
        if (!teacherId) {
            console.error('No teacher ID found');
            return;
        }
        
        console.log('Opening modal for teacher ID:', teacherId);
        currentTeacherId = teacherId;
        openModal(teacherId);
    }

    /**
     * Handle edit teacher button click
     */
    function handleEditTeacher(e) {
        e.preventDefault();
        const teacherId = $(this).data('teacher-id');
        
        // Placeholder for edit functionality
        alert(`Edit functionality for teacher ID: ${teacherId} will be implemented`);
    }

    /**
     * Handle generate CV button click
     */
    function handleGenerateCV(e) {
        e.preventDefault();
        const $button = $(this);
        const teacherId = $button.data('teacher-id');
        const teacherName = $button.data('teacher-name');
        
        generateTeacherCV(teacherId, teacherName, $button);
    }

    /**
     * Handle download CV from modal
     */
    function handleDownloadCV(e) {
        e.preventDefault();
        
        if (currentTeacherData) {
            generateTeacherCV(
                currentTeacherData.id,
                currentTeacherData.name,
                $(this)
            );
        }
    }

    /**
     * Handle refresh data button click
     */
    function handleRefreshData(e) {
        e.preventDefault();
        const $button = $(this);
        const originalHtml = $button.html();
        
        $button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Refreshing...');
        
        setTimeout(() => {
            location.reload();
        }, 500);
    }

    /**
     * Handle export data button click
     */
    function handleExportData(e) {
        e.preventDefault();
        
        // Placeholder for export functionality
        alert('Export functionality will be implemented');
    }

    /**
     * Handle tab switching in modal
     */
    function handleTabSwitch(e) {
        e.preventDefault();
        const $button = $(this);
        const tabName = $button.data('tab');
        
        // Update tab buttons
        $('.tab-btn').removeClass('active border-gray-600 text-gray-900')
                     .addClass('border-transparent text-gray-500');
        
        $button.removeClass('border-transparent text-gray-500')
               .addClass('active border-gray-600 text-gray-900');
        
        // Update tab content
        $('.tab-content').addClass('hidden');
        $(`#${tabName}Tab`).removeClass('hidden');
    }

    /**
     * Open teacher modal
     */
    function openModal(teacherId) {
        $('#teacherModal').removeClass('hidden');
        $('#modalLoading').show();
        $('.tab-content').addClass('hidden');
        $('#personalTab').removeClass('hidden');
        
        // Reset tab buttons
        $('.tab-btn').removeClass('active border-gray-600 text-gray-900')
                     .addClass('border-transparent text-gray-500');
        $('.tab-btn[data-tab="personal"]').removeClass('border-transparent text-gray-500')
                                          .addClass('active border-gray-600 text-gray-900');
        
        // Load teacher data
        loadTeacherData(teacherId);
    }

    /**
     * Close modal
     */
    function closeModal() {
        $('#teacherModal').addClass('hidden');
        currentTeacherData = null;
        currentTeacherId = null;
    }

    /**
     * Load teacher data for modal
     */
    function loadTeacherData(teacherId) {
        // Show loading state
        $('#modalLoading').show();
        
        console.log('Loading teacher data for ID:', teacherId);
        
        // Fetch teacher data from API
        $.ajax({
            url: `/principal/api/teacher/profile-enhanced`,
            method: 'GET',
            data: { teacher_id: teacherId },
            success: function(response) {
                console.log('API Response:', response);
                if (response.success && response.teacher) {
                    currentTeacherData = response.teacher;
                    populateModal(response.teacher);
                } else {
                    console.error('Failed to load teacher data:', response.message);
                    showModalError('Failed to load teacher data: ' + (response.message || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading teacher data:', error);
                console.error('XHR Status:', xhr.status);
                console.error('Response Text:', xhr.responseText);
                showModalError('Error loading teacher data: ' + error);
            },
            complete: function() {
                $('#modalLoading').hide();
            }
        });
    }

    /**
     * Populate modal with teacher data
     */
    function populateModal(data) {
        console.log('Populating modal with data:', data);
        
        // Update modal title and general info
        const teacherName = data.displayName || data.name || data.full_name || 'Teacher';
        const designation = data.designation || 'Teacher';
        const department = data.department || 'Academic Department';
        
        $('#modalTitle').text(`${teacherName} - Details`);
        $('#modalSubtitle').text(`${designation} • ${department}`);
        
        // Update teacher general info section
        const initials = teacherName.split(' ').map(n => n[0]).join('').toUpperCase();
        $('#teacherInitials').text(initials);
        $('#teacherName').text(teacherName);
        $('#teacherDesignation').text(`${designation} • ${department}`);
        $('#teacherContact').text(`${data.email || 'No email'} • ${data.phone || 'No phone'}`);
        $('#employeeId').text(data.employee_id || 'No ID');
        $('#experienceInfo').text(`${data.total_experience_years || 0} years experience`);
        
        // Update status badge
        if (data.is_active) {
            $('#statusBadge').removeClass('bg-red-100 text-red-800').addClass('bg-green-100 text-green-800').text('Active');
        } else {
            $('#statusBadge').removeClass('bg-green-100 text-green-800').addClass('bg-red-100 text-red-800').text('Inactive');
        }
        
        // Populate personal information tab
        populatePersonalTab(data);
        
        // Populate other tabs with enhanced data
        populateEducationTab(data);
        populateExperienceTab(data);
        populateSkillsTab(data);
        populateAchievementsTab(data);
    }

    /**
     * Populate personal information tab
     */
    function populatePersonalTab(data) {
        $('#modalDateOfBirth').text(data.date_of_birth || '-');
        $('#modalGender').text(data.gender || '-');
        $('#modalUsername').text(data.username || '-');
        $('#modalJoiningDate').text(data.joining_date || '-');
        $('#modalEmergencyContact').text(data.emergency_contact || '-');
        $('#modalAddress').text(data.address || '-');
        $('#modalOfficeLocation').text(data.office_location || '-');
        $('#modalLanguages').text(data.languages_known || '-');
    }

    /**
     * Populate education tab
     */
    function populateEducationTab(data) {
        const $content = $('#modalEducationContent');

        // Check for enhanced education timeline data first
        if (data.educationTimeline && data.educationTimeline.length > 0) {
            let html = '<div class="space-y-4">';
            data.educationTimeline.forEach(edu => {
                html += `
                    <div class="border-l-4 border-gray-300 pl-4">
                        <h5 class="font-semibold text-gray-900">${edu.title || 'Qualification'}</h5>
                        <p class="text-sm text-gray-600">${edu.institution || 'Institution'}</p>
                        <div class="text-xs text-gray-500 mt-1">
                            <span>${edu.year || 'Year'}</span>
                            ${edu.percentage ? ` • ${edu.percentage}%` : ''}
                            ${edu.grade ? ` • Grade: ${edu.grade}` : ''}
                            ${edu.cgpa ? ` • CGPA: ${edu.cgpa}` : ''}
                        </div>
                        ${edu.specialization ? `<p class="text-xs text-gray-600 mt-1">Specialization: ${edu.specialization}</p>` : ''}
                        ${edu.board ? `<p class="text-xs text-gray-600">Board/University: ${edu.board}</p>` : ''}
                    </div>
                `;
            });
            html += '</div>';
            $content.html(html);
        } else {
            $content.html('<p class="text-sm text-gray-500">No education data available</p>');
        }
    }

    /**
     * Populate experience tab
     */
    function populateExperienceTab(data) {
        const $content = $('#modalExperienceContent');

        // Check for enhanced experience timeline data first
        if (data.experienceTimeline && data.experienceTimeline.length > 0) {
            let html = '<div class="space-y-4">';
            data.experienceTimeline.forEach(exp => {
                const borderColor = exp.isCurrent ? 'border-blue-400' : 'border-gray-300';
                html += `
                    <div class="border-l-4 ${borderColor} pl-4">
                        <div class="flex items-center gap-2">
                            <h5 class="font-semibold text-gray-900">${exp.title || 'Position'}</h5>
                            ${exp.isCurrent ? '<span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">Current</span>' : ''}
                        </div>
                        <p class="text-sm text-gray-600">${exp.institution || 'Organization'}</p>
                        <p class="text-xs text-gray-500">${exp.duration || 'Duration'}</p>
                        ${exp.description ? `<p class="text-sm text-gray-700 mt-2">${exp.description}</p>` : ''}
                    </div>
                `;
            });
            html += '</div>';
            $content.html(html);
        } else {
            $content.html('<p class="text-sm text-gray-500">No experience data available</p>');
        }
    }

    /**
     * Populate skills tab
     */
    function populateSkillsTab(data) {
        const $skillsContent = $('#modalSkillsContent');
        const $certificationsContent = $('#modalCertificationsContent');

        // Skills
        if (data.skillsByCategory && Object.keys(data.skillsByCategory).length > 0) {
            let skillsHtml = '<div class="space-y-4">';
            Object.entries(data.skillsByCategory).forEach(([category, skills]) => {
                skillsHtml += `
                    <div>
                        <h5 class="font-semibold text-gray-900 mb-2 capitalize">${category.replace('_', ' ')}</h5>
                        <div class="flex flex-wrap gap-2">
                `;
                skills.forEach(skill => {
                    skillsHtml += `<span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">${skill.name || skill}</span>`;
                });
                skillsHtml += `</div></div>`;
            });
            skillsHtml += '</div>';
            $skillsContent.html(skillsHtml);
        } else if (data.special_skills) {
            const skills = data.special_skills.split(',').map(s => s.trim());
            let skillsHtml = '<div class="flex flex-wrap gap-2">';
            skills.forEach(skill => {
                skillsHtml += `<span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">${skill}</span>`;
            });
            skillsHtml += '</div>';
            $skillsContent.html(skillsHtml);
        } else {
            $skillsContent.html('<p class="text-sm text-gray-500">No skills data available</p>');
        }

        // Certifications
        if (data.certifications && data.certifications.length > 0) {
            let certHtml = '<div class="space-y-3">';
            data.certifications.forEach(cert => {
                certHtml += `
                    <div class="border-l-4 border-gray-200 pl-3">
                        <p class="font-semibold text-gray-900">${cert.name || 'Certification'}</p>
                        <p class="text-xs text-gray-600">${cert.issuer || 'Issuer'}</p>
                        <p class="text-xs text-gray-500">${cert.issueDate ? new Date(cert.issueDate).toLocaleDateString() : 'Date not specified'}</p>
                    </div>
                `;
            });
            certHtml += '</div>';
            $certificationsContent.html(certHtml);
        } else if (data.professional_certifications) {
            const certs = data.professional_certifications.split(',').map(c => c.trim());
            let certHtml = '<div class="space-y-2">';
            certs.forEach(cert => {
                certHtml += `<div class="text-sm"><p class="font-semibold text-gray-900">${cert}</p></div>`;
            });
            certHtml += '</div>';
            $certificationsContent.html(certHtml);
        } else {
            $certificationsContent.html('<p class="text-sm text-gray-500">No certifications available</p>');
        }
    }

    /**
     * Populate achievements tab
     */
    function populateAchievementsTab(data) {
        const $content = $('#modalAchievementsContent');

        if (data.achievementsByCategory && Object.keys(data.achievementsByCategory).length > 0) {
            let html = '<div class="space-y-6">';
            Object.entries(data.achievementsByCategory).forEach(([category, achievements]) => {
                html += `
                    <div>
                        <h5 class="font-semibold text-gray-900 mb-3 capitalize">${category.replace('_', ' ')}</h5>
                        <div class="space-y-3">
                `;
                achievements.forEach(achievement => {
                    html += `
                        <div class="border-l-4 border-gray-300 pl-4">
                            <h6 class="font-semibold text-gray-900">${achievement.title || 'Achievement'}</h6>
                            <p class="text-sm text-gray-600">${achievement.description || 'Description'}</p>
                            <p class="text-xs text-gray-500">${achievement.date ? new Date(achievement.date).toLocaleDateString() : 'Date not specified'}</p>
                        </div>
                    `;
                });
                html += `</div></div>`;
            });
            html += '</div>';
            $content.html(html);
        } else {
            $content.html('<p class="text-sm text-gray-500">No achievements recorded</p>');
        }
    }

    /**
     * Show modal error
     */
    function showModalError(message) {
        $('#modalLoading').hide();
        $('.tab-content').addClass('hidden');
        $('#personalTab').removeClass('hidden').html(`
            <div class="p-6 text-center">
                <i class="fas fa-exclamation-triangle text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Data</h3>
                <p class="text-sm text-gray-500">${message}</p>
            </div>
        `);
    }

    /**
     * Generate teacher CV PDF
     */
    function generateTeacherCV(teacherId, teacherName, $button) {
        const originalHtml = $button.html();
        $button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating...');

        setTimeout(() => {
            try {
                // Use current teacher data if available, otherwise create basic data
                let teacher = currentTeacherData || {
                    id: teacherId,
                    name: teacherName || 'Teacher Name',
                    designation: 'Teacher',
                    department: 'Academic Department',
                    employee_id: `EMP${String(teacherId).padStart(4, '0')}`,
                    email: '<EMAIL>',
                    phone: 'Not provided',
                    joining_date: new Date().toISOString().split('T')[0],
                    employment_type: 'Permanent',
                    is_active: true
                };

                const cvHTML = `
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>${teacher.name || 'Teacher'} - Curriculum Vitae</title>
                        <style>
                            @page { size: A4; margin: 15mm; }
                            body { font-family: Arial, sans-serif; margin: 0; padding: 0; line-height: 1.4; color: #333; }
                            .header { text-align: center; border-bottom: 3px solid #374151; padding-bottom: 15px; margin-bottom: 20px; }
                            .header h1 { color: #374151; margin: 0; font-size: 24px; }
                            .header h2 { color: #6b7280; margin: 5px 0; font-size: 16px; font-weight: normal; }
                            .section { margin-bottom: 20px; }
                            .section-title { background: #374151; color: white; padding: 8px 15px; margin-bottom: 10px; font-weight: bold; }
                            .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px; }
                            .info-item { margin-bottom: 8px; }
                            .info-label { font-weight: bold; color: #374151; }
                            .footer { text-align: center; margin-top: 30px; padding-top: 15px; border-top: 1px solid #ccc; font-size: 12px; color: #666; }
                            @media print { .no-print { display: none; } }
                        </style>
                    </head>
                    <body>
                        <div class='no-print' style='background: #f3f4f6; padding: 15px; margin-bottom: 20px; border-radius: 5px; border: 1px solid #d1d5db;'>
                            <h3 style='margin: 0 0 10px 0; color: #374151;'>✅ Teacher CV Document</h3>
                            <p style='margin: 0 0 10px 0;'>Use <strong>Ctrl+P</strong> (Windows) or <strong>Cmd+P</strong> (Mac) to save as PDF</p>
                            <button onclick='window.print()' style='background: #374151; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px; cursor: pointer;'>Print as PDF</button>
                            <button onclick='window.close()' style='background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;'>Close</button>
                        </div>
                        <div class='header'>
                            <h1>${teacher.name || 'Teacher Name'}</h1>
                            <h2>${teacher.designation || 'Teacher'}</h2>
                            <p>${teacher.department || 'Academic Department'}</p>
                        </div>
                        <div class='section'>
                            <div class='section-title'>PERSONAL INFORMATION</div>
                            <div class='info-grid'>
                                <div>
                                    <div class='info-item'><span class='info-label'>Employee ID:</span> ${teacher.employee_id || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Email:</span> ${teacher.email || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Phone:</span> ${teacher.phone || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Date of Birth:</span> ${teacher.date_of_birth || 'N/A'}</div>
                                </div>
                                <div>
                                    <div class='info-item'><span class='info-label'>Gender:</span> ${teacher.gender || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Joining Date:</span> ${teacher.joining_date || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Employment Type:</span> ${teacher.employment_type || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Status:</span> ${teacher.is_active ? 'Active' : 'Inactive'}</div>
                                </div>
                            </div>
                        </div>
                        <div class='section'>
                            <div class='section-title'>PROFESSIONAL EXPERIENCE</div>
                            <div class='info-item'><span class='info-label'>Total Experience:</span> ${teacher.total_experience_years || 0} years</div>
                            <div class='info-item'><span class='info-label'>Teaching Experience:</span> ${teacher.teaching_experience_years || 0} years</div>
                            <div class='info-item'><span class='info-label'>Subjects Taught:</span> ${teacher.subjects_taught || 'N/A'}</div>
                        </div>
                        <div class='section'>
                            <div class='section-title'>CONTACT & ADMINISTRATIVE DETAILS</div>
                            <div class='info-grid'>
                                <div>
                                    <div class='info-item'><span class='info-label'>Office Location:</span> ${teacher.office_location || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Emergency Contact:</span> ${teacher.emergency_contact || 'N/A'}</div>
                                </div>
                                <div>
                                    <div class='info-item'><span class='info-label'>Address:</span> ${teacher.address || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Languages Known:</span> ${teacher.languages_known || 'N/A'}</div>
                                </div>
                            </div>
                        </div>
                        <div class='footer'>
                            <p><strong>Generated on:</strong> ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
                            <p>Teacher Details System - Curriculum Vitae</p>
                        </div>
                    </body>
                    </html>
                `;

                const cvWindow = window.open('', '_blank');
                cvWindow.document.write(cvHTML);
                cvWindow.document.close();

                $button.html('<i class="fas fa-check mr-2"></i>CV Generated!');
                setTimeout(() => {
                    $button.html(originalHtml);
                }, 2000);

                console.log('✅ CV generated successfully for:', teacher.name);

            } catch(error) {
                console.error('❌ CV generation error:', error);
                $button.html('<i class="fas fa-times mr-2"></i>Error');
                setTimeout(() => {
                    $button.html(originalHtml);
                }, 2000);
            }
        }, 500);
    }

    // Expose functions for debugging
    window.teacherDetails = {
        openModal,
        closeModal,
        generateTeacherCV,
        filterTeachers
    };

    console.log('✅ Teacher Details System Ready');
});
