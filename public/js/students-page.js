// Centralized Students Page JavaScript with AJAX
var StudentsPage = {
  // Initialize the page
  init: function() {
    if (this.initialized) {
      console.log('StudentsPage already initialized, skipping...');
      return;
    }

    console.log('StudentsPage initialized with AJAX');
    this.bindEvents();
    this.currentFilters = this.getUrlParams();
    this.initialized = true;
  },

  // Get URL parameters
  getUrlParams: function() {
    var params = {};
    var urlParams = new URLSearchParams(window.location.search);
    for (var pair of urlParams.entries()) {
      params[pair[0]] = pair[1];
    }
    return params;
  },

  // Show loading indicator
  showLoading: function() {
    if ($('#loadingIndicator').length === 0) {
      $('body').append('<div id="loadingIndicator" style="position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:rgba(0,0,0,0.8);color:white;padding:20px;border-radius:5px;z-index:9999;">Loading...</div>');
    }
  },

  // Hide loading indicator
  hideLoading: function() {
    $('#loadingIndicator').remove();
  },

  // Bind all events
  bindEvents: function() {
    var self = this;

    // Toggle filters
    $(document).on('click', '#toggleFilters', function(e) {
      e.preventDefault();
      console.log('Toggle filters clicked');
      self.toggleAdvancedFilters();
    });

    // Quick filters with AJAX
    $(document).on('click', '.quick-filter', function(e) {
      e.preventDefault();
      var filterType = $(this).data('filter');
      console.log('Quick filter clicked:', filterType);
      self.applyQuickFilterAjax(filterType);
    });

    // Clear filters with AJAX
    $(document).on('click', '#clearFilters', function(e) {
      e.preventDefault();
      console.log('Clear filters clicked');
      self.clearAllFiltersAjax();
    });

    // View details button (expandable rows)
    $(document).on('click', '.view-details-btn', function(e) {
      e.preventDefault();
      e.stopPropagation();
      var studentId = $(this).data('student-id');
      console.log('View details button clicked:', studentId);
      self.toggleStudentDetails(studentId);
    });

    // View student button (modal) - handle the actual button class used in HTML
    $(document).on('click', '.view-student-btn', function(e) {
      e.preventDefault();
      e.stopPropagation();
      var studentId = $(this).data('student-id');
      console.log('View student button clicked:', studentId);
      self.openStudentModal(studentId);
    });

    // Modal close buttons
    $(document).on('click', '#closeStudentModalBtn, #closeStudentModalFooterBtn', function(e) {
      e.preventDefault();
      self.closeStudentModal();
    });

    // Select all checkbox functionality
    $(document).on('change', '#selectAll', function() {
      var isChecked = $(this).is(':checked');
      $('.student-checkbox').prop('checked', isChecked);
      self.updateSelectedCount();
      self.updateExportButtons();
    });

    // Individual checkbox functionality
    $(document).on('change', '.student-checkbox', function() {
      self.updateSelectAllState();
      self.updateSelectedCount();
      self.updateExportButtons();
    });

    // Records per page change
    $(document).on('change', '#recordsPerPage', function() {
      var newLimit = $(this).val();
      var currentUrl = new URL(window.location);
      currentUrl.searchParams.set('limit', newLimit);
      currentUrl.searchParams.set('page', '1'); // Reset to first page
      window.location.href = currentUrl.toString();
    });

    // Jump to page functionality
    $(document).on('click', '#jumpToPageBtn', function() {
      var pageNumber = $('#jumpToPage').val();
      var maxPages = $('#jumpToPage').attr('max');

      if (pageNumber && pageNumber >= 1 && pageNumber <= maxPages) {
        var currentUrl = new URL(window.location);
        currentUrl.searchParams.set('page', pageNumber);
        window.location.href = currentUrl.toString();
      }
    });

    // Jump to page on Enter key
    $(document).on('keypress', '#jumpToPage', function(e) {
      if (e.which === 13) { // Enter key
        $('#jumpToPageBtn').click();
      }
    });

    // Export selected students
    $(document).on('click', '#exportSelectedBtn', function() {
      self.exportSelectedStudents();
    });

    // Export all students
    $(document).on('click', '#exportAllBtn', function() {
      self.exportAllStudents();
    });
  },

  // Toggle advanced filters
  toggleAdvancedFilters: function() {
    var $advancedFilters = $('#advancedFilters');
    var $filterToggleIcon = $('#filterToggleIcon');

    if ($advancedFilters.hasClass('hidden')) {
      $advancedFilters.removeClass('hidden');
      $filterToggleIcon.addClass('rotated');
      console.log('Filters expanded');
    } else {
      $advancedFilters.addClass('hidden');
      $filterToggleIcon.removeClass('rotated');
      console.log('Filters collapsed');
    }
  },

  // Apply quick filter with AJAX
  applyQuickFilterAjax: function(filterType) {
    console.log('Applying quick filter:', filterType);
    // For now, just redirect to test
    var url = '/principal/students?';
    switch(filterType) {
      case 'male':
        url += 'gender=Male';
        break;
      case 'female':
        url += 'gender=Female';
        break;
      case 'bpl':
        url += 'bpl=Yes';
        break;
      case 'disability':
        url += 'disability=Yes';
        break;
    }
    window.location.href = url;
  },

  // Clear all filters with AJAX
  clearAllFiltersAjax: function() {
    window.location.href = '/principal/students';
  },

  // Toggle student details
  toggleStudentDetails: function(studentId) {
    var $detailsRow = $('#details-' + studentId);

    if ($detailsRow.length) {
      if ($detailsRow.hasClass('hidden')) {
        // Hide all other open details first
        $('[id^="details-"]').addClass('hidden');
        // Show this one
        $detailsRow.removeClass('hidden');
        console.log('Student details expanded for:', studentId);
      } else {
        $detailsRow.addClass('hidden');
        console.log('Student details collapsed for:', studentId);
      }
    }
  },

  // Current student data for modal
  currentStudentData: null,

  // Modal functions
  openStudentModal: function(studentId) {
    var modal = document.getElementById('studentModal');

    if (!modal) {
      console.error('Student modal not found');
      return;
    }

    console.log('🔍 Opening student modal for ID:', studentId);

    // Show modal with loading state
    modal.classList.remove('hidden');
    this.showLoadingState();

    // Fetch student data
    fetch(`/principal/students/${studentId}`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          console.log('✅ Student data loaded:', data.student.name);
          this.currentStudentData = data.student;
          this.populateStudentModal(data.student);
          this.initializeTabFunctionality();
        } else {
          console.error('❌ Failed to load student data:', data.message);
          this.showErrorState('Student not found');
        }
      })
      .catch(error => {
        console.error('❌ Error loading student data:', error);
        this.showErrorState('Error loading student data');
      });
  },

  // Show loading state in modal
  showLoadingState: function() {
    // Show loading in general info section
    document.getElementById('studentName').textContent = 'Loading...';
    document.getElementById('studentClass').textContent = 'Loading student information...';
    document.getElementById('studentContact').textContent = '';
    document.getElementById('studentId').textContent = '';
    document.getElementById('studentSession').textContent = '';
    document.getElementById('studentInitials').textContent = '...';
  },

  // Show error state in modal
  showErrorState: function(message) {
    document.getElementById('studentName').textContent = 'Error';
    document.getElementById('studentClass').textContent = message;
    document.getElementById('studentContact').textContent = '';
    document.getElementById('studentId').textContent = '';
    document.getElementById('studentSession').textContent = '';
    document.getElementById('studentInitials').textContent = '!';
  },

  // Populate student modal with data
  populateStudentModal: function(student) {
    console.log('📋 Populating modal with student data');

    // Populate general information (always visible)
    this.populateGeneralInfo(student);

    // Populate all tab contents
    this.populatePersonalTab(student);
    this.populateAcademicTab(student);
    this.populateContactTab(student);
    this.populateAdministrativeTab(student);
    this.populateHealthTab(student);

    // Update last updated timestamp
    const lastUpdated = student.updated_at ? new Date(student.updated_at).toLocaleDateString() : 'Unknown';
    document.getElementById('lastUpdated').textContent = lastUpdated;
  },

  // Populate general information section
  populateGeneralInfo: function(student) {
    // Student name and initials
    const name = student.name || 'Unknown Student';
    document.getElementById('studentName').textContent = name;
    document.getElementById('studentInitials').textContent = name.charAt(0).toUpperCase();

    // Class information
    const classInfo = [
      student.class || '',
      student.section || '',
      student.stream || ''
    ].filter(Boolean).join(' • ') || 'Class information not available';
    document.getElementById('studentClass').textContent = classInfo;

    // Contact information
    const contactInfo = [
      student.student_id || '',
      student.contact_no || ''
    ].filter(Boolean).join(' • ') || 'Contact information not available';
    document.getElementById('studentContact').textContent = contactInfo;

    // Student ID and session
    document.getElementById('studentId').textContent = student.student_id || 'N/A';
    document.getElementById('studentSession').textContent = student.session || 'N/A';
  },

  // Populate Personal Information Tab
  populatePersonalTab: function(student) {
    document.getElementById('personalName').textContent = student.name || '-';
    document.getElementById('personalDOB').textContent = student.dob ? new Date(student.dob).toLocaleDateString('en-IN') : '-';
    document.getElementById('personalGender').textContent = student.gender || '-';
    document.getElementById('personalReligion').textContent = student.religion_name || '-';
    document.getElementById('personalCaste').textContent = student.caste_category_name || '-';
    document.getElementById('personalFatherName').textContent = student.father_name || '-';
    document.getElementById('personalMotherName').textContent = student.mother_name || '-';
    document.getElementById('personalContact').textContent = student.contact_no || '-';
  },

  // Populate Academic Details Tab
  populateAcademicTab: function(student) {
    document.getElementById('academicClass').textContent = student.class || '-';
    document.getElementById('academicSection').textContent = student.section || '-';
    document.getElementById('academicStream').textContent = student.stream || '-';
    document.getElementById('academicTrade').textContent = student.trade || '-';
    document.getElementById('academicRollNo').textContent = student.roll_no || '-';
    document.getElementById('academicSession').textContent = student.session || '-';
    document.getElementById('academicMedium').textContent = student.medium_name || '-';
    document.getElementById('academicRoom').textContent = student.room_number || '-';
  },

  // Populate Contact & Address Tab
  populateContactTab: function(student) {
    document.getElementById('contactAddress').textContent = student.cur_address || '-';
    document.getElementById('contactVillage').textContent = student.village_ward || '-';
    document.getElementById('contactGramPanchayat').textContent = student.gram_panchayat || '-';
    document.getElementById('contactPinCode').textContent = student.pin_code || '-';
    document.getElementById('contactDistrict').textContent = student.district_name || '-';
    document.getElementById('contactState').textContent = student.state_name || '-';
    document.getElementById('contactPhone').textContent = student.contact_no || '-';
  },

  // Populate Administrative Tab
  populateAdministrativeTab: function(student) {
    document.getElementById('adminAdmissionNo').textContent = student.admission_no || '-';
    document.getElementById('adminAdmissionDate').textContent = student.admission_date ? new Date(student.admission_date).toLocaleDateString('en-IN') : '-';
    document.getElementById('adminUdiseCode').textContent = student.udise_code || '-';
    document.getElementById('adminBankName').textContent = student.bank_name || '-';
    document.getElementById('adminIFSC').textContent = student.ifsc_code || '-';
    document.getElementById('adminAccountHolder').textContent = student.account_holder_name || '-';
    document.getElementById('adminAccountCode').textContent = student.account_holder_code || '-';
  },

  // Populate Health & Welfare Tab
  populateHealthTab: function(student) {
    document.getElementById('healthHeight').textContent = student.height ? `${student.height} cm` : '-';
    document.getElementById('healthWeight').textContent = student.weight ? `${student.weight} kg` : '-';
    document.getElementById('healthDisability').textContent = student.disability || 'No';
    document.getElementById('healthBPL').textContent = student.bpl || 'No';
  },

  // Initialize tab functionality
  initializeTabFunctionality: function() {
    console.log('🔧 Initializing tab functionality');

    // Get all tab buttons
    const tabButtons = document.querySelectorAll('.student-tab-btn');

    // Add click event listeners to tab buttons
    tabButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const targetTab = e.currentTarget.getAttribute('data-tab');
        this.switchTab(targetTab);
      });
    });

    // Set default active tab (personal)
    this.switchTab('personal');

    // Add event listeners for modal buttons
    this.initializeModalEventListeners();
  },

  // Initialize modal event listeners
  initializeModalEventListeners: function() {
    // Close button event listeners
    const closeButtons = ['closeStudentModalBtn', 'closeStudentModalFooterBtn'];
    closeButtons.forEach(buttonId => {
      const button = document.getElementById(buttonId);
      if (button) {
        button.addEventListener('click', () => {
          this.closeStudentModal();
        });
      }
    });

    // PDF download button
    const pdfButton = document.getElementById('downloadStudentPDFBtn');
    if (pdfButton) {
      pdfButton.addEventListener('click', () => {
        this.downloadStudentPDF();
      });
    }
  },

  // Download student PDF
  downloadStudentPDF: function() {
    if (!this.currentStudentData) {
      console.error('No student data available for PDF generation');
      return;
    }

    console.log('🔄 Generating student PDF for:', this.currentStudentData.name);

    const pdfButton = document.getElementById('downloadStudentPDFBtn');
    const originalHtml = pdfButton.innerHTML;

    // Show loading state
    pdfButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    pdfButton.disabled = true;

    // For now, create a simple PDF content (can be enhanced later with server-side generation)
    setTimeout(() => {
      try {
        this.generateStudentPDFContent();

        // Reset button state
        pdfButton.innerHTML = '<i class="fas fa-check"></i>';
        setTimeout(() => {
          pdfButton.innerHTML = originalHtml;
          pdfButton.disabled = false;
        }, 2000);

      } catch (error) {
        console.error('Error generating PDF:', error);
        pdfButton.innerHTML = '<i class="fas fa-times"></i>';
        setTimeout(() => {
          pdfButton.innerHTML = originalHtml;
          pdfButton.disabled = false;
        }, 2000);
      }
    }, 500);
  },

  // Generate student PDF content with Arial 16px and category-wise organization
  generateStudentPDFContent: function() {
    const student = this.currentStudentData;

    const pdfContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${student.name || 'Student'} - Profile</title>
        <meta charset="UTF-8">
        <style>
          @page {
            size: A4;
            margin: 15mm;
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
          }
          * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
          }
          body {
            font-family: Arial, sans-serif;
            font-size: 16px;
            line-height: 1.4;
            color: #000;
            background: white;
            width: 100%;
          }
          .container {
            width: 100%;
            max-width: 210mm;
            margin: 0 auto;
          }
          .header {
            text-align: center;
            border-bottom: 3px solid #000;
            padding-bottom: 15px;
            margin-bottom: 20px;
          }
          .header h1 {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            text-transform: uppercase;
          }
          .header h2 {
            font-size: 18px;
            margin-bottom: 5px;
            font-weight: normal;
          }
          .header p {
            font-size: 14px;
            color: #333;
          }
          .category-section {
            margin-bottom: 25px;
            border: 2px solid #000;
          }
          .category-title {
            background: #000;
            color: white;
            padding: 10px 15px;
            font-weight: bold;
            font-size: 18px;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 1px;
          }
          .category-content {
            padding: 15px;
          }
          .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 10px;
          }
          .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px dotted #ccc;
          }
          .info-label {
            font-weight: bold;
            width: 50%;
            flex-shrink: 0;
          }
          .info-value {
            width: 50%;
            text-align: right;
            word-wrap: break-word;
          }
          .single-column {
            grid-template-columns: 1fr;
          }
          .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 15px;
            border-top: 2px solid #000;
            font-size: 14px;
          }
          .no-print {
            background: #f5f5f5;
            padding: 15px;
            margin-bottom: 20px;
            border: 2px solid #333;
            text-align: center;
            border-radius: 5px;
          }
          .no-print h3 {
            margin-bottom: 10px;
            font-size: 20px;
            color: #333;
          }
          .no-print p {
            margin-bottom: 15px;
            font-size: 16px;
          }
          .no-print button {
            background: #000;
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 0 10px;
            cursor: pointer;
            font-size: 16px;
            border-radius: 3px;
            font-family: Arial, sans-serif;
          }
          .no-print button:hover {
            background: #333;
          }
          @media print {
            .no-print { display: none !important; }
            body { font-size: 14px; }
            .header h1 { font-size: 22px; }
            .header h2 { font-size: 16px; }
            .category-title { font-size: 16px; }
            .info-item { font-size: 14px; }
          }
        </style>
      </head>
      <body>
        <div class='no-print'>
          <h3>📄 Student Profile Document</h3>
          <p>Use <strong>Ctrl+P</strong> (Windows) or <strong>Cmd+P</strong> (Mac) to save as PDF</p>
          <button onclick='window.print()'>🖨️ Print as PDF</button>
          <button onclick='window.close()'>❌ Close</button>
        </div>

        <div class='container'>
          <div class='header'>
            <h1>${student.name || 'Student Name'}</h1>
            <h2>Student ID: ${student.student_id || 'N/A'}</h2>
            <p>${student.class || ''} ${student.section || ''} ${student.stream || ''} | Session: ${student.session || 'N/A'}</p>
          </div>

          <!-- PERSONAL INFORMATION CATEGORY -->
          <div class='category-section'>
            <div class='category-title'>Personal Information</div>
            <div class='category-content'>
              <div class='info-grid'>
                <div class='info-item'>
                  <span class='info-label'>Full Name:</span>
                  <span class='info-value'>${student.name || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Student ID:</span>
                  <span class='info-value'>${student.student_id || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Father's Name:</span>
                  <span class='info-value'>${student.father_name || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Mother's Name:</span>
                  <span class='info-value'>${student.mother_name || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Date of Birth:</span>
                  <span class='info-value'>${student.dob ? new Date(student.dob).toLocaleDateString('en-IN') : 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Gender:</span>
                  <span class='info-value'>${student.gender || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Religion:</span>
                  <span class='info-value'>${student.religion_name || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Caste Category:</span>
                  <span class='info-value'>${student.caste_category_name || 'N/A'}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- ACADEMIC DETAILS CATEGORY -->
          <div class='category-section'>
            <div class='category-title'>Academic Details</div>
            <div class='category-content'>
              <div class='info-grid'>
                <div class='info-item'>
                  <span class='info-label'>Class:</span>
                  <span class='info-value'>${student.class || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Section:</span>
                  <span class='info-value'>${student.section || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Stream:</span>
                  <span class='info-value'>${student.stream || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Trade:</span>
                  <span class='info-value'>${student.trade || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Roll Number:</span>
                  <span class='info-value'>${student.roll_no || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Session:</span>
                  <span class='info-value'>${student.session || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Medium:</span>
                  <span class='info-value'>${student.medium_name || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Room Number:</span>
                  <span class='info-value'>${student.room_number || 'N/A'}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- CONTACT & ADDRESS CATEGORY -->
          <div class='category-section'>
            <div class='category-title'>Contact & Address</div>
            <div class='category-content'>
              <div class='info-grid'>
                <div class='info-item'>
                  <span class='info-label'>Contact Number:</span>
                  <span class='info-value'>${student.contact_no || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Village/Ward:</span>
                  <span class='info-value'>${student.village_ward || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Gram Panchayat:</span>
                  <span class='info-value'>${student.gram_panchayat || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Pin Code:</span>
                  <span class='info-value'>${student.pin_code || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>District:</span>
                  <span class='info-value'>${student.district_name || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>State:</span>
                  <span class='info-value'>${student.state_name || 'N/A'}</span>
                </div>
              </div>
              <div class='info-grid single-column'>
                <div class='info-item'>
                  <span class='info-label'>Current Address:</span>
                  <span class='info-value'>${student.cur_address || 'N/A'}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- ADMINISTRATIVE DETAILS CATEGORY -->
          <div class='category-section'>
            <div class='category-title'>Administrative Details</div>
            <div class='category-content'>
              <div class='info-grid'>
                <div class='info-item'>
                  <span class='info-label'>Admission Number:</span>
                  <span class='info-value'>${student.admission_no || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Admission Date:</span>
                  <span class='info-value'>${student.admission_date ? new Date(student.admission_date).toLocaleDateString('en-IN') : 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>UDISE Code:</span>
                  <span class='info-value'>${student.udise_code || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Bank Name:</span>
                  <span class='info-value'>${student.bank_name || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>IFSC Code:</span>
                  <span class='info-value'>${student.ifsc_code || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Account Holder:</span>
                  <span class='info-value'>${student.account_holder_name || 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Account Holder Code:</span>
                  <span class='info-value'>${student.account_holder_code || 'N/A'}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- HEALTH & WELFARE CATEGORY -->
          <div class='category-section'>
            <div class='category-title'>Health & Welfare</div>
            <div class='category-content'>
              <div class='info-grid'>
                <div class='info-item'>
                  <span class='info-label'>Height:</span>
                  <span class='info-value'>${student.height ? student.height + ' cm' : 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Weight:</span>
                  <span class='info-value'>${student.weight ? student.weight + ' kg' : 'N/A'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>BPL Status:</span>
                  <span class='info-value'>${student.bpl || 'No'}</span>
                </div>
                <div class='info-item'>
                  <span class='info-label'>Disability Status:</span>
                  <span class='info-value'>${student.disability || 'No'}</span>
                </div>
              </div>
            </div>
          </div>

          <div class='footer'>
            <p><strong>Student Management System</strong></p>
            <p>Generated on ${new Date().toLocaleDateString('en-IN')} at ${new Date().toLocaleTimeString('en-IN')}</p>
          </div>
        </div>
      </body>
      </html>
    `;

    // Open PDF in new browser tab with proper window features
    const pdfWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');

    if (pdfWindow) {
      pdfWindow.document.open();
      pdfWindow.document.write(pdfContent);
      pdfWindow.document.close();

      // Focus the new window
      pdfWindow.focus();

      console.log('✅ Student PDF generated successfully for:', student.name);
    } else {
      console.error('❌ Failed to open PDF window - popup blocked?');
      alert('PDF generation failed. Please allow popups for this site and try again.');
    }
  },

  // Switch between tabs
  switchTab: function(tabName) {
    console.log('🔄 Switching to tab:', tabName);

    // Remove active state from all tabs
    document.querySelectorAll('.student-tab-btn').forEach(btn => {
      btn.classList.remove('border-principal-primary', 'text-principal-primary');
      btn.classList.add('border-transparent', 'text-gray-500');
    });

    // Hide all tab contents
    document.querySelectorAll('.student-tab-content').forEach(content => {
      content.classList.add('hidden');
    });

    // Activate selected tab
    const activeTabBtn = document.getElementById(tabName + 'Tab');
    const activeTabContent = document.getElementById(tabName + 'TabContent');

    if (activeTabBtn && activeTabContent) {
      activeTabBtn.classList.remove('border-transparent', 'text-gray-500');
      activeTabBtn.classList.add('border-principal-primary', 'text-principal-primary');
      activeTabContent.classList.remove('hidden');
    }
  },

  closeStudentModal: function() {
    var modal = document.getElementById('studentModal');
    if (modal) {
      modal.classList.add('hidden');
      this.currentStudentData = null;
    }
  },

  // Update select all checkbox state
  updateSelectAllState: function() {
    var totalCheckboxes = $('.student-checkbox').length;
    var checkedCheckboxes = $('.student-checkbox:checked').length;

    if (checkedCheckboxes === 0) {
      $('#selectAll').prop('indeterminate', false).prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
      $('#selectAll').prop('indeterminate', false).prop('checked', true);
    } else {
      $('#selectAll').prop('indeterminate', true).prop('checked', false);
    }
  },

  // Update selected count display
  updateSelectedCount: function() {
    var selectedCount = $('.student-checkbox:checked').length;
    $('#selectedCount').text(selectedCount);
  },

  // Update export buttons visibility
  updateExportButtons: function() {
    var selectedCount = $('.student-checkbox:checked').length;

    if (selectedCount > 0) {
      $('#exportSelectedBtn').removeClass('hidden');
    } else {
      $('#exportSelectedBtn').addClass('hidden');
    }
  },

  // Export selected students
  exportSelectedStudents: function() {
    var selectedStudents = [];

    $('.student-checkbox:checked').each(function() {
      try {
        var studentData = JSON.parse($(this).attr('data-student-data'));
        selectedStudents.push(studentData);
      } catch (e) {
        console.error('Error parsing student data:', e);
      }
    });

    if (selectedStudents.length === 0) {
      alert('Please select students to export.');
      return;
    }

    this.downloadCSV(selectedStudents, 'selected_students_export.csv');
  },

  // Export all students (with current filters)
  exportAllStudents: function() {
    // Get current URL parameters to maintain filters
    var currentUrl = new URL(window.location);
    var exportUrl = '/principal/students/export?' + currentUrl.searchParams.toString();

    // Create a temporary link and trigger download
    var link = document.createElement('a');
    link.href = exportUrl;
    link.download = 'all_students_export.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  },

  // Download CSV helper function
  downloadCSV: function(data, filename) {
    if (data.length === 0) {
      alert('No data to export.');
      return;
    }

    // Define CSV headers
    var headers = [
      'S.No', 'Student ID', 'UdiseCode', 'Name', 'Father Name', 'Mother Name', 'DOB', 'Gender',
      'Class', 'Section', 'Stream', 'Trade', 'Caste Category', 'BPL', 'Disability',
      'Religion', 'Medium', 'Height', 'Weight', 'Admission No', 'Admission Date',
      'State', 'District', 'Address', 'Village/Ward', 'Gram Panchayat', 'Pin Code',
      'Roll No', 'Contact No', 'IFSC Code', 'Bank Name', 'Account Holder',
      'Account Holder Name', 'Account Holder Code', 'Session'
    ];

    // Convert data to CSV format
    var csvContent = headers.join(',') + '\n';

    data.forEach(function(student, index) {
      var row = [
        index + 1,
        student.student_id || '',
        student.udise_code || '',
        student.name || '',
        student.father_name || '',
        student.mother_name || '',
        student.dob ? new Date(student.dob).toLocaleDateString('en-IN') : '',
        student.gender || '',
        student.class || '',
        student.section || '',
        student.stream || '',
        student.trade || '',
        student.caste_category_name || '',
        student.bpl || '',
        student.disability || '',
        student.religion_name || '',
        student.medium_name || '',
        student.height || '',
        student.weight || '',
        student.admission_no || '',
        student.admission_date ? new Date(student.admission_date).toLocaleDateString('en-IN') : '',
        student.state_name || '',
        student.district_name || '',
        student.cur_address || '',
        student.village_ward || '',
        student.gram_panchayat || '',
        student.pin_code || '',
        student.roll_no || '',
        student.contact_no || '',
        student.ifsc_code || '',
        student.bank_name || '',
        student.account_holder || '',
        student.account_holder_name || '',
        student.account_holder_code || '',
        student.session || ''
      ];

      // Escape commas and quotes in data
      var escapedRow = row.map(function(field) {
        var fieldStr = String(field);
        if (fieldStr.includes(',') || fieldStr.includes('"') || fieldStr.includes('\n')) {
          return '"' + fieldStr.replace(/"/g, '""') + '"';
        }
        return fieldStr;
      });

      csvContent += escapedRow.join(',') + '\n';
    });

    // Create and download the file
    var blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    var link = document.createElement('a');

    if (link.download !== undefined) {
      var url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
};

// Initialize when document is ready
$(document).ready(function() {
  console.log('External JS: Document ready - initializing StudentsPage');
  console.log('External JS: jQuery available:', typeof $ !== 'undefined');
  console.log('External JS: Elements found:');
  console.log('- Toggle button:', $('#toggleFilters').length);
  console.log('- Quick filters:', $('.quick-filter').length);
  console.log('- Student rows:', $('.student-row').length);
  console.log('- View details buttons:', $('.view-details-btn').length);
  console.log('- View student buttons:', $('.view-student-btn').length);
  console.log('- Student modal:', $('#studentModal').length);

  StudentsPage.init();
});
